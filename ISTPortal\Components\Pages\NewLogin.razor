﻿@page "/login-not-in-user"
@layout Layout.BlankLayout
@inject NavigationManager Navigation

<style>
    body {
        margin: 0;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #e6f0fa, #f0f7fd);
        background-attachment: fixed;
    }

    .login-container {
        display: flex;
        height: 100vh;
        justify-content: center;
        align-items: center;
    }

    .login-panel {
        background-color: white;
        padding: 2.5rem 2rem;
        border-radius: 14px;
        box-shadow: 0 6px 18px rgba(0, 0, 0, 0.1);
        width: 360px;
        animation: fadeIn 0.5s ease-in-out;
    }

    .login-header {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 2rem;
    }

    .logo-img {
        width: 48px;
        height: 48px;
        margin-right: 0.75rem;
    }

    .portal-title {
        font-size: 1.6rem;
        color: #036ac4;
        font-weight: 600;
        margin: 0;
    }

    .form-group {
        text-align: left;
        margin-bottom: 1.2rem;
    }

    label {
        display: block;
        font-weight: 600;
        margin-bottom: 0.4rem;
        color: #333;
    }

    .form-control {
        width: 100%;
        padding: 0.6rem;
        border: 1px solid #ccc;
        border-radius: 8px;
        font-size: 1rem;
        transition: border-color 0.3s ease;
    }

        .form-control:focus {
            border-color: #036ac4;
            outline: none;
        }

    .btn-login {
        width: 100%;
        background-color: #036ac4;
        color: white;
        border: none;
        padding: 0.75rem;
        font-size: 1rem;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.3s ease;
        margin-top: 1rem;
    }

        .btn-login:hover {
            background-color: #0256a1;
        }
    fadeIn {
        from {opacity: 0;transform: translateY(20px);}
        to {opacity: 1;transform: translateY(0);}
    }


</style>

<div class="login-container">
    <div class="login-panel">
        <div class="login-header">
            <img src="/images/wh-logo.png" alt="Logo" class="logo-img" />
            <h1 class="portal-title">IST PORTAL</h1>
        </div>

        <div class="form-group">
            <label for="userId">User ID</label>
            <input type="text" id="userId" @bind="userId" class="form-control" />
        </div>

        <div class="form-group">
            <label for="password">Password</label>
            <input type="password" id="password" @bind="password" class="form-control" />
        </div>

        <button class="btn-login" @onclick="HandleLogin">Login</button>
    </div>
</div>

@code {
    private string userId;
    private string password;

    private void HandleLogin()
    {
        // In real-world apps, validate credentials securely
        Navigation.NavigateTo("/home");
    }
}
