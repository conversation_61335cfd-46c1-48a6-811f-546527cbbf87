@page "/login"
@using ISTPortal.Data
@using ISTPortal.Models
@using ISTPortal.Components.Authentication
@using Microsoft.EntityFrameworkCore
@using System.ComponentModel.DataAnnotations
@layout Layout.BlankLayout
@inject NavigationManager NavigationManager
@inject ApplicationDbContext DbContext
@inject AuthenticationStateProvider AuthStateProvider

<PageTitle>Login - IST Portal</PageTitle>

<div class="login-container">
    <FluentCard>
        <div class="login-form">
            <div class="login-header">
                <h2>IST Portal</h2>
                <p>Please sign in to continue</p>
            </div>

            @if (!string.IsNullOrEmpty(errorMessage))
            {
                <FluentMessageBar Intent="MessageIntent.Error">@errorMessage</FluentMessageBar>
            }

            @if (isLoading)
            {
                <FluentProgressRing></FluentProgressRing>
                <p>Signing in...</p>
            }
            else
            {
                <EditForm Model="@loginModel" OnValidSubmit="@HandleLogin" FormName="LoginForm">
                    <DataAnnotationsValidator />

                    <div class="form-group">
                        <FluentTextField @bind-Value="loginModel.UserId"
                                       Placeholder="User ID"
                                       Required="true"
                                       Disabled="@isLoading" />
                        <ValidationMessage For="@(() => loginModel.UserId)" />
                    </div>

                    <div class="form-group">
                        <FluentTextField @bind-Value="loginModel.Password"
                                       Type="password"
                                       Placeholder="Password"
                                       Required="true"
                                       Disabled="@isLoading" />
                        <ValidationMessage For="@(() => loginModel.Password)" />
                    </div>

                    <FluentButton Type="Microsoft.FluentUI.AspNetCore.Components.ButtonType.Submit"
                                Appearance="Appearance.Accent"
                                Disabled="@isLoading"
                                Style="width: 100%;">
                        Login
                    </FluentButton>
                </EditForm>
            }
        </div>
    </FluentCard>
</div>

<style>
    .login-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 20px;
    }

    .login-form {
        padding: 2rem;
        width: 100%;
        max-width: 400px;
    }

    .login-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .login-header h2 {
        color: var(--accent-fill-rest);
        margin-bottom: 0.5rem;
    }

    .login-header p {
        color: var(--neutral-foreground-hint);
        margin: 0;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    fluent-text-field {
        width: 100%;
    }

    fluent-message-bar {
        margin-bottom: 1rem;
    }

    fluent-progress-ring {
        margin: 1rem auto;
        display: block;
    }
</style>

@code {
    private LoginModel loginModel = new();
    private string errorMessage = "";
    private bool isLoading = false;

    public class LoginModel
    {
        [Required(ErrorMessage = "User ID is required")]
        public string UserId { get; set; } = "";

        [Required(ErrorMessage = "Password is required")]
        public string Password { get; set; } = "";
    }

    protected override async Task OnInitializedAsync()
    {
        // Check if user is already authenticated
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true)
        {
            NavigationManager.NavigateTo("/", replace: true);
        }
    }

    private async Task HandleLogin()
    {
        errorMessage = "";
        isLoading = true;
        StateHasChanged();

        try
        {
            var user = await DbContext.Users
                .FirstOrDefaultAsync(u => u.UserId == loginModel.UserId &&
                                        u.Password == loginModel.Password &&
                                        u.IsActive);

            if (user != null)
            {
                var customAuthStateProvider = (CustomAuthenticationStateProvider)AuthStateProvider;
                var success = await customAuthStateProvider.SignInAsync(user);

                if (success)
                {
                    NavigationManager.NavigateTo("/", replace: true);
                }
                else
                {
                    errorMessage = "Authentication failed. Please try again.";
                }
            }
            else
            {
                errorMessage = "Invalid credentials or account is inactive.";
            }
        }
        catch (Exception ex)
        {
            errorMessage = "An error occurred during login. Please try again.";
            if (ex.InnerException != null)
                errorMessage += " - Detail: " + ex.InnerException.Message;
            Console.WriteLine($"Login error: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }
}
