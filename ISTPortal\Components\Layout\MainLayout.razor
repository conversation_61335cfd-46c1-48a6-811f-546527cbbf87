﻿@inherits LayoutComponentBase

<FluentDialogProvider />
<SfDialogProvider />

<FluentLayout>
    <FluentHeader>
        <div style="display: flex; justify-content: space-between; width: 100%; align-items: center;">
            <span>
                <img src="/images/wh-logo.png" style="width:50px;height:50px;" />
                <span style="font-size: larger;font-weight: 500;font-family: sans-serif;">IST Portal</span>
            </span>
            <AuthorizeView>
                <Authorized>
                    <span>@context.User.Claims.FirstOrDefault(c => c.Type == System.Security.Claims.ClaimTypes.GivenName)?.Value</span>
                </Authorized>
            </AuthorizeView>
        </div>
    </FluentHeader>
    <FluentStack Class="main" Orientation="Microsoft.FluentUI.AspNetCore.Components.Orientation.Horizontal" Width="100%">
        <NavMenu />
        <FluentBodyContent Class="body-content">
            <div class="content">
                @Body
            </div>
        </FluentBodyContent>
    </FluentStack>

</FluentLayout>

<div id="blazor-error-ui" data-nosnippet>
    An unhandled error has occurred.
    <a href="." class="reload">Reload</a>
    <span class="dismiss">🗙</span>
</div>
