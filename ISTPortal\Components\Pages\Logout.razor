@page "/logout"
@using ISTPortal.Components.Authentication
@layout Layout.BlankLayout
@inject CustomAuthenticationStateProvider AuthProvider
@inject NavigationManager NavigationManager

<PageTitle>Logging out...</PageTitle>

<div class="logout-container">
    <FluentCard>
        <div class="logout-content">
            <FluentProgressRing></FluentProgressRing>
            <h3>Logging out...</h3>
            <p>Please wait while we sign you out.</p>
        </div>
    </FluentCard>
</div>

<style>
    .logout-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .logout-content {
        text-align: center;
        padding: 2rem;
        min-width: 300px;
    }

    fluent-progress-ring {
        margin-bottom: 1rem;
    }

    h3 {
        margin: 1rem 0 0.5rem 0;
        color: var(--accent-fill-rest);
    }

    p {
        margin: 0;
        color: var(--neutral-foreground-hint);
    }
</style>

@code {
    protected override async Task OnInitializedAsync()
    {
        try
        {
            await AuthProvider.SignOutAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Logout error: {ex.Message}");
        }
        finally
        {
            // Always redirect to login after a short delay
            await Task.Delay(1000);
            NavigationManager.NavigateTo("/login", replace: true);
        }
    }
}
