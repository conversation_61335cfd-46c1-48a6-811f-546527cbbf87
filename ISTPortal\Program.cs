using ISTPortal.Components;
using Microsoft.FluentUI.AspNetCore.Components;
using Syncfusion.Blazor;
using Microsoft.AspNetCore.Components.Authorization;
using ISTPortal.Components.Authentication;
using Microsoft.EntityFrameworkCore;
using ISTPortal.Models;
using ISTPortal.Data;
using ISTPortal.Services;
using Syncfusion.Blazor.Popups;

namespace ISTPortal
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);
            Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense("MzY2NDQzOUAzMjM4MmUzMDJlMzBkT1liN3Z3aGwzb0c4YmNNNUhOd2NzWHp6RUFVVGVNL0Y1Z2x3bW1FYzJJPQ==");

            // Add services to the container.
            builder.Services.AddRazorComponents()
                .AddInteractiveServerComponents();
            
            // Add authentication services
            builder.Services.AddAuthenticationCore();
            builder.Services.AddScoped<AuthenticationStateProvider, CustomAuthenticationStateProvider>();
            
            // Add DbContext
            builder.Services.AddDbContext<ApplicationDbContext>(options =>
                options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

            builder.Services.AddFluentUIComponents();
            builder.Services.AddSyncfusionBlazor();

            builder.Services.AddScoped<ISTDataService>();
            builder.Services.AddScoped<SfDialogService>();

            // Setup services
            builder.Services.AddScoped<UserService>();
            builder.Services.AddScoped<Services.MenuService>();
            builder.Services.AddScoped<StoreService>();
            builder.Services.AddScoped<UserRightsService>();

            var app = builder.Build();

            if (!app.Environment.IsDevelopment())
            {
                app.UseExceptionHandler("/Error");
                app.UseHsts();
            }

            app.UseHttpsRedirection();
            app.UseStaticFiles();
            app.UseAntiforgery();

            app.MapRazorComponents<App>()
                .AddInteractiveServerRenderMode();

            app.Run();
        }
    }
}
