﻿@page "/planning/istdest"
@using ISTPortal.DTO
@using ISTPortal.Services
@using Syncfusion.Blazor.Cards
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Notifications
@inject Microsoft.FluentUI.AspNetCore.Components.IDialogService DialogService
@inject Syncfusion.Blazor.Popups.SfDialogService sfDialogService
@inject NavigationManager NavigationManager
@inject ISTDataService istService
@inject IJSRuntime JS

<style>
    .componentWidth {
        width: -webkit-fill-available;
    }

    label {
        padding-left: 2px;
    }

    .gridlbl {
        font-size: 12px;
        font-weight: 500;
    }

    .cardrow {
        border: 1px solid #ccc;
        border-radius: 5px;
        padding: 10px;
        margin: 0px;
        background-color: #036ac4;
        color: white;
    }

    .file-input {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        cursor: pointer;
    }

    .e-grid td.e-active {
        background: powderblue !important;
    }
</style>


<FluentBreadcrumb>
    <FluentBreadcrumbItem Href="/home">
        <FluentIcon Value="@(new Icons.Regular.Size16.Home())" Color="@Color.Neutral" Slot="start" />
    </FluentBreadcrumbItem>
    <FluentBreadcrumbItem Href="/planning/istdest">
        Receiving Store
    </FluentBreadcrumbItem>
</FluentBreadcrumb>

@* <div class="row mt-2">
    <div class="col-2">
        <FluentDatePicker Label="Start Date" AriaLabel="Start Date" Class="componentWidth" @bind-Value="@filter.fromDate" />
    </div>
    <div class="col-2">
        <FluentDatePicker Label="End Date" Class="componentWidth" AriaLabel="End Date" @bind-Value="@filter.toDate" />
    </div>
    <div class="col-md-3">
        <FluentTextField Class="componentWidth" @bind-Value="filter.ISTCode" Label="IST Code"></FluentTextField>
    </div>
</div>

<FluentStack HorizontalGap="15" Class="mt-2 mb-2">
    <FluentButton IconStart="@(new Icons.Regular.Size16.Search())"
                  BackgroundColor="#00c853" Color="white">
        Search
    </FluentButton>
</FluentStack> *@

<SfToast @ref="toastObj"></SfToast>

<SfGrid @ref="dgIST"
        DataSource="@istList"
        AllowFiltering="true">
    <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
    <GridColumns>
        <GridColumn Field=@nameof(ISTDTO.IstCode) HeaderText="IST Code" AutoFit="true">
            <Template>
                @{
                    var ctx = (context as ISTDTO);
                    <div class="row">
                        <div class="col">
                            <span class="gridlbl">@ctx!.IstCode</span>
                        </div>
                    </div>
                }
            </Template>
        </GridColumn>
        <GridColumn Field=@nameof(ISTDTO.TONumber) HeaderText="Source TO #" AutoFit="true">
            <Template>
                @{
                    var ctx = (context as ISTDTO);
                    <div class="row">
                        <div class="col">
                            <span class="gridlbl">@ctx!.SourceTONumber</span><br />
                            <FluentBadge Appearance="Appearance.Accent">@ctx.SourceTOStatus</FluentBadge>
                        </div>
                        <div class="col">
                        </div>
                    </div>
                }
            </Template>
        </GridColumn>
        <GridColumn Field=@nameof(ISTDTO.CWHTONumber) HeaderText="CWH TO #" AutoFit="true">
            <Template>
                @{
                    var ctx = (context as ISTDTO);
                    <div class="row">
                        <div class="col">
                            <span class="gridlbl">@ctx!.CWHTONumber</span><br />
                            @if (@ctx.CWHTOStatus != "" && @ctx.CWHTOStatus != null)
                            {
                                <FluentBadge Appearance="Appearance.Accent">@ctx.CWHTOStatus</FluentBadge>
                            }
                        </div>
                    </div>
                }
            </Template>
        </GridColumn>
        <GridColumn HeaderText="IST Item Qty" AutoFit="true">
            <Template>
                @{
                    var ctx = (context as ISTDTO);
                    <div class="row">
                        <div class="col">
                            <span class="gridlbl">Total Items : @ctx!.TotalItems</span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <span class="gridlbl">Total Qty. : @ctx.TotalQty</span>
                        </div>
                    </div>
                }
            </Template>
        </GridColumn>
        <GridColumn HeaderText="Source Item Qty" AutoFit="true">
            <Template>
                @{
                    var ctx = (context as ISTDTO);
                    <div class="row">
                        <div class="col">
                            <span class="gridlbl">Total Items : @ctx!.SrcTotalItems</span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <span class="gridlbl">Total Qty. : @ctx.SrcTotalQty</span>
                        </div>
                    </div>
                }
            </Template>
        </GridColumn>
        <GridColumn HeaderText="CWH Item Qty" AutoFit="true">
            <Template>
                @{
                    var ctx = (context as ISTDTO);
                    <div class="row">
                        <div class="col">
                            <span class="gridlbl">Total Items : @ctx!.CWHTotalItems</span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <span class="gridlbl">Total Qty. : @ctx.CWHTotalQty</span>
                        </div>
                    </div>
                }
            </Template>
        </GridColumn>
        <GridColumn HeaderText="Stores" AutoFit="true">
            <Template>
                @{
                    var ctx = (context as ISTDTO);
                    <div class="row">
                        <div class="col">
                            <span class="gridlbl">Source : @ctx!.fromStore</span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <span class="gridlbl">Destination : @ctx.toStore</span>
                        </div>
                    </div>
                }
            </Template>
        </GridColumn>
        <GridColumn HeaderText="Status" AutoFit="true">
            <Template>
                @{
                    var ctx = (context as ISTDTO);
                    <div class="row">
                        <div class="col">
                            @if (ctx!.destToStatus != null && ctx.destToStatus != "")
                            {
                                <FluentBadge Appearance="Appearance.Accent">@ctx!.destToStatus</FluentBadge>
                            }
                        </div>
                    </div>
                }
            </Template>
        </GridColumn>
        <GridColumn HeaderText=" " AutoFit="true">
            <Template>
                @{
                    var ctx = (context as ISTDTO);
                    var isDisable = false;
                    if (ctx!.destToStatus != null && ctx.destToStatus != "")
                    {
                        isDisable = true;
                    }
                    <SfButton  CssClass="e-small e-info e-flat"
                              IconCss="e-icons e-edit" OnClick="@(() =>viewTONumber(ctx))">
                        View
                    </SfButton>
                    <SfButton Disabled=isDisable
                              CssClass="e-small e-warning e-flat" IconCss="e-icons e-hand-gestures"
                              OnClick="@(() =>receiveTONumber(ctx))">
                        Receive
                    </SfButton>
                }
            </Template>
        </GridColumn>
    </GridColumns>
</SfGrid>

<SfDialog ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" Width="750px" Visible="false" @ref="dlgViewIST">
    <DialogTemplates>
        <Header>VIEW</Header>
        <Content>
            <div class="row">
                <div class="col-md">
                    <b>Source TO #:</b> @newISTStoreBatch.SourceTONumber
                </div>
                <div class="col-md">
                    <b>CWH TO #:</b> @newISTStoreBatch.CWHTONumber
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md">
                    <b>IST Code :</b> @newISTStoreBatch.IstCode
                </div>
                <div class="col-md">
                    <b>IST Title :</b> @newISTStoreBatch.IstTitle
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md">
                    <b>Source Store :</b> @newISTStoreBatch.fromStore
                </div>
                <div class="col-md">
                    <b>Destination Store :</b> @newISTStoreBatch.toStore
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md">
                    <b>Remarks :</b> @newISTStoreBatch.IstDescription
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md">
                    <SfGrid @ref="dgISTDetail"
                            DataSource="@newISTStoreBatch.istCWHBatchDetail"
                            AllowFiltering="true" Height="220">
                        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                        <GridColumns>
                            <GridColumn Field=@nameof(CWHBatchDetailDTO.ItemCode) HeaderText="Item Barcode" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(CWHBatchDetailDTO.ItemQty) HeaderText="Item Quantity" TextAlign="TextAlign.Right" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(CWHBatchDetailDTO.ItemDesc) HeaderText="Item Desc." TextAlign="TextAlign.Left" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(CWHBatchDetailDTO.HIR1) HeaderText="HIR1" TextAlign="TextAlign.Left" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(CWHBatchDetailDTO.HIR2) HeaderText="HIR2" TextAlign="TextAlign.Left" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(CWHBatchDetailDTO.HIR3) HeaderText="HIR3" TextAlign="TextAlign.Left" AutoFit>
                            </GridColumn>
                            @* <GridColumn Field=@nameof(CWHBatchDetailDTO.CWHBatchDetailRemarks) HeaderText="Remarks" Width="200">
                            </GridColumn> *@
                        </GridColumns>
                    </SfGrid>
                </div>
            </div>

            <FluentStack HorizontalGap="15" Class="mt-2 mb-2">
                <FluentButton IconStart="@(new Icons.Regular.Size16.LockClosed())"
                              Appearance="Appearance.Accent" OnClick="@(() => dlgViewIST!.HideAsync())">
                    Close
                </FluentButton>
            </FluentStack>
        </Content>
    </DialogTemplates>
</SfDialog>

<SfDialog ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" Width="750px" Visible="false" @ref="dlgReceiveIST">
    <DialogTemplates>
        <Header>Acknowledge</Header>
        <Content>
            <div class="row">
                <div class="col-md">
                    <b>Source TO #:</b> @newISTStoreBatch.SourceTONumber
                </div>
                <div class="col-md">
                    <b>CWH TO #:</b> @newISTStoreBatch.CWHTONumber
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md">
                    <b>IST Code :</b> @newISTStoreBatch.IstCode
                </div>
                <div class="col-md">
                    <b>IST Title :</b> @newISTStoreBatch.IstTitle
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md">
                    <b>Source Store :</b> @newISTStoreBatch.fromStore
                </div>
                <div class="col-md">
                    <b>Destination Store :</b> @newISTStoreBatch.toStore
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md">
                    <b>Remarks :</b> @newISTStoreBatch.IstDescription
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md">
                    <SfGrid @ref="dgISTDetail"
                            DataSource="@newISTStoreBatch.istCWHBatchDetail"
                            AllowFiltering="true" Height="220">
                        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                        <GridColumns>
                            <GridColumn Field=@nameof(CWHBatchDetailDTO.ItemCode) HeaderText="Item Barcode" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(CWHBatchDetailDTO.ItemQtyReadOnly) HeaderText="Rec. Qty." TextAlign="TextAlign.Right" Width="100">
                            </GridColumn>
                            <GridColumn Field=@nameof(CWHBatchDetailDTO.ItemDesc) HeaderText="Item Desc." TextAlign="TextAlign.Left" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(CWHBatchDetailDTO.HIR1) HeaderText="HIR1" TextAlign="TextAlign.Left" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(CWHBatchDetailDTO.HIR2) HeaderText="HIR2" TextAlign="TextAlign.Left" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(CWHBatchDetailDTO.HIR3) HeaderText="HIR3" TextAlign="TextAlign.Left" AutoFit>
                            </GridColumn>
                            @* <GridColumn Field=@nameof(CWHBatchDetailDTO.ItemQty) HeaderText="Qty." TextAlign="TextAlign.Right" Width="100">
                            </GridColumn> 
                            <GridColumn HeaderText="Qty." TextAlign="TextAlign.Right" Width="100">
                                <Template>
                                    @{
                                        var ctx = (context as CWHBatchDetailDTO);
                                        <FluentNumberField Min="0" Step="1" @bind-Value="ctx!.ItemQty" />
                                    }
                                </Template>
                            </GridColumn>
                            <GridColumn HeaderText="Remarks" Width="230">
                                <Template>
                                    @{
                                        var ctx = (context as CWHBatchDetailDTO);
                                        <FluentTextField Class="componentWidth" @bind-Value="ctx!.CWHBatchDetailRemarks"></FluentTextField>
                                    }
                                </Template>
                            </GridColumn>*@
                        </GridColumns>
                    </SfGrid>
                </div>
            </div>

            <FluentStack HorizontalGap="15" Class="mt-2 mb-2">
                <FluentButton IconStart="@(new Icons.Regular.Size16.CalendarCancel())"
                              Appearance="Appearance.Accent" OnClick="receiveTO">
                    Received
                </FluentButton>
                <FluentButton IconStart="@(new Icons.Regular.Size16.LockClosed())"
                              Appearance="Appearance.Accent" OnClick="@(() => dlgReceiveIST!.HideAsync())">
                    Close
                </FluentButton>
            </FluentStack>
        </Content>
    </DialogTemplates>
</SfDialog>



@code {
    private filterForm filter = new();
    private SfToast? toastObj;

    private ISTDTO newISTStoreBatch = new();
    private string loginId = "admin";
    private List<ISTDTO> istList = new();
    private SfGrid<ISTDTO>? dgIST;
    private SfGrid<CWHBatchDetailDTO>? dgISTDetail;
    private SfDialog? dlgViewIST, dlgReceiveIST;

    private async Task LoadISTDEST()
    {
        istList = await istService.getISTForDEST(loginId, filter);
        StateHasChanged();
    }

    protected override async Task OnInitializedAsync()
    {
        try
        {
            await LoadISTDEST();
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message.ToString());
        }
    }

    private async void viewTONumber(ISTDTO pIstMaster)
    {
        try
        {
            newISTStoreBatch = new();
            newISTStoreBatch = pIstMaster;
            newISTStoreBatch.istCWHBatchDetail = await istService.getCWHItems(pIstMaster.storeBatchIdCWH);
            await dlgViewIST!.ShowAsync();
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");
        }
    }

    private async void receiveTONumber(ISTDTO pIstMaster)
    {
        try
        {
            newISTStoreBatch = new();
            newISTStoreBatch = pIstMaster;
            newISTStoreBatch.istCWHBatchDetail = await istService.getCWHItems(pIstMaster.storeBatchIdCWH);
            await dlgReceiveIST!.ShowAsync();
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");
        }
    }

    private async void ShowToast(string title, string message, string type)
    {
        if (toastObj == null) return;

        var toast = new ToastModel
            {
                Title = title,
                Content = message,
                CssClass = type == "error" ? "e-toast-danger" : "e-toast-info",
                ShowCloseButton = true,
                Timeout = 5000
            };

        await toastObj.ShowAsync(toast);
    }

    private async void receiveTO()
    {
        try
        {
            if (newISTStoreBatch != null)
            {
                if (!newISTStoreBatch.istCWHBatchDetail.Any())
                {
                    await sfDialogService.AlertAsync("Item list is required.", "Info");
                    return;
                }

                bool isISTAvailable = await istService.getISTStatus(newISTStoreBatch.IstId ?? 0);
                if (isISTAvailable == false)
                {
                    await sfDialogService.AlertAsync("IST is already closed.", "Info");
                    return;
                }

                await istService.receiveTOItemsForDest(newISTStoreBatch, "DESTINATION", loginId);
                await LoadISTDEST();
                await dlgReceiveIST!.HideAsync();
            }
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");
        }
    }

}
