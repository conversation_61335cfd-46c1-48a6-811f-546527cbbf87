﻿<div class="navmenu">
    <input type="checkbox" title="Menu expand/collapse toggle" id="navmenu-toggle" class="navmenu-icon" />
    <label for="navmenu-toggle" class="navmenu-icon"><FluentIcon Value="@(new Icons.Regular.Size20.Navigation())" Color="Color.Fill" /></label>
    <nav class="sitenav" aria-labelledby="main-menu">
        <FluentNavMenu Id="main-menu" Collapsible="true" Width="250" Title="Navigation menu" @bind-Expanded="expanded" CustomToggle="true">
            
            
            <AuthorizeView>
                <Authorized>
                    <FluentNavLink Href="/home" Match="NavLinkMatch.All" Icon="@(new Icons.Regular.Size20.Home())" IconColor="Color.Accent">Home</FluentNavLink>
               @*      <FluentNavLink Href="counter" Icon="@(new Icons.Regular.Size20.NumberSymbolSquare())" IconColor="Color.Accent">Counter</FluentNavLink>
                    <FluentNavLink Href="weather" Icon="@(new Icons.Regular.Size20.WeatherPartlyCloudyDay())" IconColor="Color.Accent">Weather</FluentNavLink> *@
                </Authorized>
                <NotAuthorized>
                    @* <FluentNavLink Href="/login" Icon="@(new Icons.Regular.Size20.SignOut())" IconColor="Color.Accent">Login</FluentNavLink> *@
                    <FluentNavLink Href="/planning/istmaster" Icon="@(new Icons.Regular.Size20.ShareScreenStart())" IconColor="Color.Accent">IST Planning</FluentNavLink>
                    <FluentNavLink Href="/planning/istsource" Icon="@(new Icons.Regular.Size20.ShareScreenStart())" IconColor="Color.Accent">Sending Store</FluentNavLink>
                    <FluentNavLink Href="/planning/istcwh" Icon="@(new Icons.Regular.Size20.ShareScreenStart())" IconColor="Color.Accent">CWH</FluentNavLink>
                    <FluentNavLink Href="/planning/istdest" Icon="@(new Icons.Regular.Size20.ShareScreenStart())" IconColor="Color.Accent">Receiving Store</FluentNavLink>
                    <FluentNavGroup Expanded="false" Title="Reports"
                                    Icon="@(new Icons.Filled.Size16.ContentView())">
                        <FluentNavLink Icon="@(new Icons.Filled.Size24.ContentViewGallery())" Href="/reports/rptistinfo">IST Info</FluentNavLink>
                    </FluentNavGroup>
                    <FluentNavGroup Expanded="false" Title="Setup"
                                    Icon="@(new Icons.Filled.Size16.Settings())">
                        <FluentNavLink Icon="@(new Icons.Filled.Size24.ContentViewGallery())" Href="/setup/menus">Menus</FluentNavLink>
                        <FluentNavLink Icon="@(new Icons.Filled.Size24.ContentViewGallery())" Href="/setup/users">Users</FluentNavLink>
                        <FluentNavLink Icon="@(new Icons.Filled.Size24.ContentViewGallery())" Href="/setup/Stores">Stores</FluentNavLink>
                    </FluentNavGroup>
                </NotAuthorized>
            </AuthorizeView>
        </FluentNavMenu>
    </nav>
</div>

@code {
    private bool expanded = true;
}
