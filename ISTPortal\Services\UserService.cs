using ISTPortal.Data;
using ISTPortal.DTO;
using ISTPortal.Models;
using Microsoft.EntityFrameworkCore;

namespace ISTPortal.Services
{
    public class UserService
    {
        private readonly ApplicationDbContext _context;

        public UserService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<UserDTO>> GetAllUsersAsync()
        {
            var users = await _context.Users
                .Where(u => u.IsActive)
                .Select(u => new UserDTO
                {
                    Id = u.Id,
                    UserId = u.UserId,
                    FullName = u.FullName,
                    IsActive = u.IsActive,
                    Password = "" // Don't return password
                })
                .ToListAsync();

            return users;
        }

        public async Task<UserDTO?> GetUserByIdAsync(int id)
        {
            var user = await _context.Users
                .Where(u => u.Id == id)
                .Select(u => new UserDTO
                {
                    Id = u.Id,
                    UserId = u.UserId,
                    FullName = u.FullName,
                    IsActive = u.IsActive,
                    Password = "" // Don't return password
                })
                .FirstOrDefaultAsync();

            if (user != null)
            {
                // Get assigned stores
                user.AssignedStoreIds = await _context.UserStores
                    .Where(us => us.UserId == id)
                    .Select(us => us.StoreId ?? 0)
                    .ToListAsync();

                // Get assigned menus
                user.AssignedMenuIds = await _context.UserMenus
                    .Where(um => um.UserId == id)
                    .Select(um => um.MenuId)
                    .ToListAsync();
            }

            return user;
        }

        public async Task<bool> CreateUserAsync(UserDTO userDto, string currentUserId)
        {
            try
            {
                var user = new User
                {
                    UserId = userDto.UserId,
                    Password = userDto.Password, // In real app, hash this
                    FullName = userDto.FullName,
                    IsActive = userDto.IsActive,
                    CreatedDate = DateTime.Now,
                    CreatedBy = currentUserId,
                    ModifiedDate = DateTime.Now,
                    ModifiedBy = currentUserId
                };

                _context.Users.Add(user);
                await _context.SaveChangesAsync();

                // Assign stores
                await AssignStoresToUserAsync(user.Id, userDto.AssignedStoreIds, currentUserId);

                // Assign menus
                await AssignMenusToUserAsync(user.Id, userDto.AssignedMenuIds);

                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateUserAsync(UserDTO userDto, string currentUserId)
        {
            try
            {
                var user = await _context.Users.FindAsync(userDto.Id);
                if (user == null) return false;

                user.UserId = userDto.UserId;
                user.FullName = userDto.FullName;
                user.IsActive = userDto.IsActive;
                user.ModifiedDate = DateTime.Now;
                user.ModifiedBy = currentUserId;

                // Only update password if provided
                if (!string.IsNullOrEmpty(userDto.Password))
                {
                    user.Password = userDto.Password; // In real app, hash this
                }

                // Update stores
                await UpdateUserStoresAsync(user.Id, userDto.AssignedStoreIds, currentUserId);

                // Update menus
                await UpdateUserMenusAsync(user.Id, userDto.AssignedMenuIds);

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteUserAsync(int id, string currentUserId)
        {
            try
            {
                var user = await _context.Users.FindAsync(id);
                if (user == null) return false;

                user.IsActive = false;
                user.ModifiedDate = DateTime.Now;
                user.ModifiedBy = currentUserId;

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        private async Task AssignStoresToUserAsync(int userId, List<int> storeIds, string currentUserId)
        {
            foreach (var storeId in storeIds)
            {
                var userStore = new UserStore
                {
                    UserId = userId,
                    StoreId = storeId,
                    CreatedBy = currentUserId,
                    CreatedDate = DateTime.Now,
                    ModifiedBy = currentUserId,
                    ModifiedDate = DateTime.Now
                };
                _context.UserStores.Add(userStore);
            }
        }

        private async Task AssignMenusToUserAsync(int userId, List<int> menuIds)
        {
            foreach (var menuId in menuIds)
            {
                var userMenu = new UserMenu
                {
                    UserId = userId,
                    MenuId = menuId
                };
                _context.UserMenus.Add(userMenu);
            }
        }

        private async Task UpdateUserStoresAsync(int userId, List<int> storeIds, string currentUserId)
        {
            // Remove existing assignments
            var existingStores = await _context.UserStores
                .Where(us => us.UserId == userId)
                .ToListAsync();
            _context.UserStores.RemoveRange(existingStores);

            // Add new assignments
            await AssignStoresToUserAsync(userId, storeIds, currentUserId);
        }

        private async Task UpdateUserMenusAsync(int userId, List<int> menuIds)
        {
            // Remove existing assignments
            var existingMenus = await _context.UserMenus
                .Where(um => um.UserId == userId)
                .ToListAsync();
            _context.UserMenus.RemoveRange(existingMenus);

            // Add new assignments
            await AssignMenusToUserAsync(userId, menuIds);
        }

        public async Task<bool> UserIdExistsAsync(string userId, int? excludeId = null)
        {
            return await _context.Users
                .AnyAsync(u => u.UserId == userId && (excludeId == null || u.Id != excludeId));
        }
    }
}
