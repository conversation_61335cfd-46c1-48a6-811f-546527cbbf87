using ISTPortal.Components;
using Microsoft.FluentUI.AspNetCore.Components;
using Syncfusion.Blazor;
using Microsoft.AspNetCore.Components.Authorization;
using ISTPortal.Components.Authentication;
using Microsoft.EntityFrameworkCore;
using ISTPortal.Models;
using ISTPortal.Data;
using ISTPortal.Services;
using Syncfusion.Blazor.Popups;
using Microsoft.AspNetCore.Authentication.Cookies;

namespace ISTPortal
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);
            Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense("MzY2NDQzOUAzMjM4MmUzMDJlMzBkT1liN3Z3aGwzb0c4YmNNNUhOd2NzWHp6RUFVVGVNL0Y1Z2x3bW1FYzJJPQ==");

            // Add services to the container.
            builder.Services.AddRazorComponents()
                .AddInteractiveServerComponents();

            // Add authentication services
            builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
                .AddCookie(options =>
                {
                    options.LoginPath = "/login";
                    options.LogoutPath = "/logout";
                    options.AccessDeniedPath = "/access-denied";
                    options.Cookie.Name = "ISTPortalAuth";
                    options.Cookie.HttpOnly = true;
                    options.Cookie.SecurePolicy = builder.Environment.IsDevelopment()
                        ? CookieSecurePolicy.SameAsRequest
                        : CookieSecurePolicy.Always;
                    options.Cookie.SameSite = SameSiteMode.Lax;
                    options.ExpireTimeSpan = TimeSpan.FromHours(8);
                    options.SlidingExpiration = true;
                });

            // Add authorization services
            builder.Services.AddAuthorization();

            // Add custom authentication state provider
            builder.Services.AddScoped<AuthenticationStateProvider, CustomAuthenticationStateProvider>();
            

            // Add DbContext
            builder.Services.AddDbContext<ApplicationDbContext>(options =>
                options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

            builder.Services.AddFluentUIComponents();
            builder.Services.AddSyncfusionBlazor();

            builder.Services.AddScoped<ISTDataService>();
            builder.Services.AddScoped<SfDialogService>();

            // Setup services
            builder.Services.AddScoped<UserService>();
            builder.Services.AddScoped<Services.MenuService>();
            builder.Services.AddScoped<StoreService>();
            builder.Services.AddScoped<UserRightsService>();
            builder.Services.AddHttpContextAccessor();

            var app = builder.Build();

            if (!app.Environment.IsDevelopment())
            {
                app.UseExceptionHandler("/Error");
                app.UseHsts();
            }

            app.UseHttpsRedirection();
            app.UseStaticFiles();
            app.UseAntiforgery();

            // Add authentication middleware
            app.UseAuthentication();
            app.UseAuthorization();

            app.MapRazorComponents<App>()
                .AddInteractiveServerRenderMode();

            app.Run();
        }
    }
}
