﻿@using ISTPortal.DTO
@using ISTPortal.Services
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@inject Services.MenuService MenuService
@inject AuthenticationStateProvider AuthStateProvider

<div class="navmenu">
    <input type="checkbox" title="Menu expand/collapse toggle" id="navmenu-toggle" class="navmenu-icon" />
    <label for="navmenu-toggle" class="navmenu-icon"><FluentIcon Value="@(new Icons.Regular.Size20.Navigation())" Color="Color.Fill" /></label>
    <nav class="sitenav" aria-labelledby="main-menu">
        <FluentNavMenu Id="main-menu" Collapsible="true" Width="250" Title="Navigation menu" @bind-Expanded="expanded" CustomToggle="true">

            <AuthorizeView>
                <Authorized>
                    <!-- Always show Home for authenticated users -->
                    <FluentNavLink Href="/home" Match="NavLinkMatch.All" Icon="@(new Icons.Regular.Size20.Home())" IconColor="Color.Accent">Home</FluentNavLink>

                    <!-- Dynamic menu items based on user rights -->
                    @if (userMenus != null)
                    {
                        @foreach (var menu in userMenus.Where(m => m.ParentMenuId == null))
                        {
                            @if (menu.HasChildren)
                            {
                                <FluentNavGroup Expanded="false" Title="@menu.MenuTitle" Icon="@GetMenuIcon(menu.MenuTitle)">
                                    @foreach (var childMenu in menu.Children)
                                    {
                                        <FluentNavLink Icon="@GetMenuIcon(childMenu.MenuTitle)" Href="@childMenu.MenuUrl">@childMenu.MenuTitle</FluentNavLink>
                                    }
                                </FluentNavGroup>
                            }
                            else
                            {
                                <FluentNavLink Href="@menu.MenuUrl" Icon="@GetMenuIcon(menu.MenuTitle)" IconColor="Color.Accent">@menu.MenuTitle</FluentNavLink>
                            }
                        }
                    }

                    <!-- Logout option -->
                    <FluentNavLink Href="/logout" Icon="@(new Icons.Regular.Size20.SignOut())" IconColor="Color.Error">Logout</FluentNavLink>
                </Authorized>
                <NotAuthorized>
                    <!-- Default menus for non-authenticated users -->
                    <FluentNavLink Href="/planning/istmaster" Icon="@(new Icons.Regular.Size20.ShareScreenStart())" IconColor="Color.Accent">IST Planning</FluentNavLink>
                    <FluentNavLink Href="/planning/istsource" Icon="@(new Icons.Regular.Size20.ShareScreenStart())" IconColor="Color.Accent">Sending Store</FluentNavLink>
                    <FluentNavLink Href="/planning/istcwh" Icon="@(new Icons.Regular.Size20.ShareScreenStart())" IconColor="Color.Accent">CWH</FluentNavLink>
                    <FluentNavLink Href="/planning/istdest" Icon="@(new Icons.Regular.Size20.ShareScreenStart())" IconColor="Color.Accent">Receiving Store</FluentNavLink>
                    <FluentNavGroup Expanded="false" Title="Reports" Icon="@(new Icons.Filled.Size16.ContentView())">
                        <FluentNavLink Icon="@(new Icons.Filled.Size24.ContentViewGallery())" Href="/reports/rptistinfo">IST Info</FluentNavLink>
                    </FluentNavGroup>
                    <FluentNavGroup Expanded="false" Title="Setup" Icon="@(new Icons.Filled.Size16.Settings())">
                        <FluentNavLink Icon="@(new Icons.Filled.Size24.ContentViewGallery())" Href="/setup/menus">Menus</FluentNavLink>
                        <FluentNavLink Icon="@(new Icons.Filled.Size24.ContentViewGallery())" Href="/setup/users">Users</FluentNavLink>
                        <FluentNavLink Icon="@(new Icons.Filled.Size24.ContentViewGallery())" Href="/setup/Stores">Stores</FluentNavLink>
                    </FluentNavGroup>
                </NotAuthorized>
            </AuthorizeView>
        </FluentNavMenu>
    </nav>
</div>

@code {
    private bool expanded = true;
    private List<MenuDTO>? userMenus;
    private int currentUserId = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadUserMenus();
    }

    private async Task LoadUserMenus()
    {
        try
        {
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true)
            {
                var userIdClaim = authState.User.FindFirst("UserDbId")?.Value;
                if (int.TryParse(userIdClaim, out currentUserId))
                {
                    userMenus = await MenuService.GetUserMenusAsync(currentUserId);
                    StateHasChanged();
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading user menus: {ex.Message}");
            userMenus = new List<MenuDTO>();
        }
    }

    private Icon GetMenuIcon(string menuTitle)
    {
        return menuTitle.ToLower() switch
        {
            "home" => new Icons.Regular.Size20.Home(),
            "planning" or "ist planning" => new Icons.Regular.Size20.ShareScreenStart(),
            "sending store" or "source" => new Icons.Regular.Size20.ShareScreenStart(),
            "cwh" => new Icons.Regular.Size20.ShareScreenStart(),
            "receiving store" or "destination" => new Icons.Regular.Size20.ShareScreenStart(),
            "reports" => new Icons.Filled.Size16.ContentView(),
            "ist info" => new Icons.Filled.Size24.ContentViewGallery(),
            "setup" => new Icons.Filled.Size16.Settings(),
            "menus" => new Icons.Regular.Size20.Navigation(),
            "users" => new Icons.Regular.Size20.People(),
            "stores" => new Icons.Regular.Size20.Building(),
            "dashboard" => new Icons.Regular.Size20.ChartMultiple(),
            "inventory" => new Icons.Regular.Size20.Box(),
            "orders" => new Icons.Regular.Size20.Cart(),
            "products" => new Icons.Regular.Size20.Cube(),
            "customers" => new Icons.Regular.Size20.PersonAccounts(),
            "suppliers" => new Icons.Regular.Size20.Building(),
            "finance" => new Icons.Regular.Size20.Money(),
            "analytics" => new Icons.Regular.Size20.DataBarVertical(),
            "settings" => new Icons.Regular.Size20.Settings(),
            "security" => new Icons.Regular.Size20.Shield(),
            "audit" => new Icons.Regular.Size20.DocumentSearch(),
            "notifications" => new Icons.Regular.Size20.Alert(),
            "help" => new Icons.Regular.Size20.QuestionCircle(),
            _ => new Icons.Filled.Size24.ContentViewGallery()
        };
    }
}
