using ISTPortal.Data;
using ISTPortal.DTO;
using ISTPortal.Models;
using Microsoft.EntityFrameworkCore;

namespace ISTPortal.Services
{
    public class MenuService
    {
        private readonly ApplicationDbContext _context;

        public MenuService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<MenuDTO>> GetAllMenusAsync()
        {
            var menus = await _context.Menus
                .Where(m => m.IsActive)
                .OrderBy(m => m.MenuSortOrder)
                .Select(m => new MenuDTO
                {
                    MenuId = m.MenuId,
                    MenuTitle = m.MenuTitle,
                    MenuUrl = m.MenuUrl ?? "",
                    ParentMenuId = m.ParentMenuId,
                    MenuSortOrder = m.MenuSortOrder,
                    IsActive = m.IsActive,
                    ParentMenuTitle = m.ParentMenu != null ? m.ParentMenu.MenuTitle : ""
                })
                .ToListAsync();

            return BuildMenuHierarchy(menus);
        }

        public async Task<List<MenuDTO>> GetMenusForTreeGridAsync()
        {
            var menus = await _context.Menus
                .Where(m => m.IsActive)
                .OrderBy(m => m.MenuSortOrder)
                .Select(m => new MenuDTO
                {
                    MenuId = m.MenuId,
                    MenuTitle = m.MenuTitle,
                    MenuUrl = m.MenuUrl ?? "",
                    ParentMenuId = m.ParentMenuId,
                    MenuSortOrder = m.MenuSortOrder,
                    IsActive = m.IsActive,
                    ParentMenuTitle = m.ParentMenu != null ? m.ParentMenu.MenuTitle : ""
                })
                .ToListAsync();

            return menus;
        }

        public async Task<List<MenuDTO>> GetUserMenusAsync(int userId)
        {
            var userMenus = await (from um in _context.UserMenus
                                  join m in _context.Menus on um.MenuId equals m.MenuId
                                  where um.UserId == userId && m.IsActive
                                  orderby m.MenuSortOrder
                                  select new MenuDTO
                                  {
                                      MenuId = m.MenuId,
                                      MenuTitle = m.MenuTitle,
                                      MenuUrl = m.MenuUrl ?? "",
                                      ParentMenuId = m.ParentMenuId,
                                      MenuSortOrder = m.MenuSortOrder,
                                      IsActive = m.IsActive,
                                      ParentMenuTitle = m.ParentMenu != null ? m.ParentMenu.MenuTitle : ""
                                  }).ToListAsync();

            return BuildMenuHierarchy(userMenus);
        }

        public async Task<MenuDTO?> GetMenuByIdAsync(int id)
        {
            return await _context.Menus
                .Where(m => m.MenuId == id)
                .Select(m => new MenuDTO
                {
                    MenuId = m.MenuId,
                    MenuTitle = m.MenuTitle,
                    MenuUrl = m.MenuUrl ?? "",
                    ParentMenuId = m.ParentMenuId,
                    MenuSortOrder = m.MenuSortOrder,
                    IsActive = m.IsActive,
                    ParentMenuTitle = m.ParentMenu != null ? m.ParentMenu.MenuTitle : ""
                })
                .FirstOrDefaultAsync();
        }

        public async Task<List<MenuDTO>> GetParentMenusAsync()
        {
            return await _context.Menus
                .Where(m => m.IsActive && m.ParentMenuId == null)
                .OrderBy(m => m.MenuSortOrder)
                .Select(m => new MenuDTO
                {
                    MenuId = m.MenuId,
                    MenuTitle = m.MenuTitle,
                    MenuUrl = m.MenuUrl ?? "",
                    ParentMenuId = m.ParentMenuId,
                    MenuSortOrder = m.MenuSortOrder,
                    IsActive = m.IsActive
                })
                .ToListAsync();
        }

        public async Task<bool> CreateMenuAsync(MenuDTO menuDto, string currentUserId)
        {
            try
            {
                var menu = new Menu
                {
                    MenuTitle = menuDto.MenuTitle,
                    MenuUrl = menuDto.MenuUrl,
                    ParentMenuId = menuDto.ParentMenuId,
                    MenuSortOrder = menuDto.MenuSortOrder,
                    IsActive = menuDto.IsActive,
                    CreatedDate = DateTime.Now,
                    CreatedBy = currentUserId,
                    ModifiedDate = DateTime.Now,
                    ModifiedBy = currentUserId
                };

                _context.Menus.Add(menu);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateMenuAsync(MenuDTO menuDto, string currentUserId)
        {
            try
            {
                var menu = await _context.Menus.FindAsync(menuDto.MenuId);
                if (menu == null) return false;

                menu.MenuTitle = menuDto.MenuTitle;
                menu.MenuUrl = menuDto.MenuUrl;
                menu.ParentMenuId = menuDto.ParentMenuId;
                menu.MenuSortOrder = menuDto.MenuSortOrder;
                menu.IsActive = menuDto.IsActive;
                menu.ModifiedDate = DateTime.Now;
                menu.ModifiedBy = currentUserId;

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteMenuAsync(int id, string currentUserId)
        {
            try
            {
                var menu = await _context.Menus.FindAsync(id);
                if (menu == null) return false;

                // Check if menu has children
                var hasChildren = await _context.Menus.AnyAsync(m => m.ParentMenuId == id && m.IsActive);
                if (hasChildren)
                {
                    return false; // Cannot delete menu with active children
                }

                menu.IsActive = false;
                menu.ModifiedDate = DateTime.Now;
                menu.ModifiedBy = currentUserId;

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> MenuTitleExistsAsync(string title, int? excludeId = null)
        {
            return await _context.Menus
                .AnyAsync(m => m.MenuTitle == title && m.IsActive && (excludeId == null || m.MenuId != excludeId));
        }

        public async Task<int> GetNextSortOrderAsync(int? parentMenuId = null)
        {
            var maxSortOrder = await _context.Menus
                .Where(m => m.ParentMenuId == parentMenuId && m.IsActive)
                .MaxAsync(m => (int?)m.MenuSortOrder) ?? 0;

            return maxSortOrder + 1;
        }

        private List<MenuDTO> BuildMenuHierarchy(List<MenuDTO> allMenus)
        {
            var menuDict = allMenus.ToDictionary(m => m.MenuId);
            var rootMenus = new List<MenuDTO>();

            foreach (var menu in allMenus)
            {
                if (menu.ParentMenuId == null)
                {
                    rootMenus.Add(menu);
                }
                else if (menuDict.ContainsKey(menu.ParentMenuId.Value))
                {
                    menuDict[menu.ParentMenuId.Value].Children.Add(menu);
                }
            }

            return rootMenus;
        }
    }
}
