using System.Security.Claims;
using Microsoft.AspNetCore.Components.Authorization;
using ISTPortal.Models;

namespace ISTPortal.Components.Authentication;

public class CustomAuthenticationStateProvider : AuthenticationStateProvider
{
    private ClaimsPrincipal anonymous = new ClaimsPrincipal(new ClaimsIdentity());

    public override Task<AuthenticationState> GetAuthenticationStateAsync()
    {
        return Task.FromResult(new AuthenticationState(anonymous));
    }

    public void NotifyUserAuthentication(User user)
    {
        var claims = new List<Claim>
        {
            new Claim(ClaimTypes.Name, user.UserId),
            new Claim(ClaimTypes.GivenName, user.FullName),
            new Claim("IsActive", user.IsActive.ToString())
        };

        var identity = new ClaimsIdentity(claims, "CustomAuth");
        var claimsPrincipal = new ClaimsPrincipal(identity);

        NotifyAuthenticationStateChanged(Task.FromResult(new AuthenticationState(claimsPrincipal)));
    }

    public void NotifyUserLogout()
    {
        NotifyAuthenticationStateChanged(Task.FromResult(new AuthenticationState(anonymous)));
    }
}
